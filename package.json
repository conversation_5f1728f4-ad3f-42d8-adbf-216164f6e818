{"name": "real-admin-ui", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@ant-design/cssinjs": "^1.24.0", "@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.10", "@ant-design/pro-layout": "^7.22.7", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "antd": "^5.26.6", "axios": "^1.11.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-fetch-backend": "^6.0.0", "isbot": "^5.1.28", "pretty-cache-header": "^1.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.6.1", "react-router": "^7.7.1", "remix-i18next": "^7.2.1", "zod": "^3.25.76"}, "devDependencies": {"@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.11", "@types/node": "^22.16.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "react-router-devtools": "^1.1.10", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4"}}