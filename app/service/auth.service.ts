const getCookie = (name: string): string | undefined => {
  // Check if we're in a browser environment
  if (typeof document === 'undefined') {
    return undefined;
  }

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift();
  return undefined;
};

interface UserInfo {
  id: string;
  sub: string;
  name: string;
  nickname: string;
  email: string;
  picture?: string;
  roles: string[];
}

export type { UserInfo };

// Helper functions to save/retrieve user info from session storage
export const saveUserInfo = (userInfo: UserInfo) => {
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('userInfo', JSON.stringify(userInfo));
  }
};

export const getSavedUserInfo = (): UserInfo | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  const savedInfo = sessionStorage.getItem('userInfo');
  if (savedInfo) {
    try {
      return JSON.parse(savedInfo);
    } catch (e) {
      console.error('Error parsing saved user info:', e);
    }
  }
  return null;
};

export const clearUserInfo = () => {
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('userInfo');
  }
};

export const fetchUserInfo = async (): Promise<UserInfo> => {
  // First check if we have user info in session storage
  const savedInfo = getSavedUserInfo();
  if (savedInfo) {
    return savedInfo;
  }

  try {
    const response = await fetch('/auth/userinfo', {
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
      },
      // Add proper CORS mode
      mode: 'cors',
    });

    if (!response.ok) {
      console.log('Response: ', response);
      throw new Error('Failed to fetch user info');
    }

    const userInfo = await response.json();
    // Save to session storage for persistence
    saveUserInfo(userInfo);
    return userInfo;
  } catch (error) {
    console.error('Error fetching user info:', error);
    throw error;
  }
};

export const logout = async (): Promise<void> => {
  // Skip during server-side rendering
  if (typeof window === 'undefined') {
    return;
  }

  // Clear user info from session storage
  clearUserInfo();

  //   // DEV MODE: Just redirect in development
  //   if (process.env.NODE_ENV === 'development') {
  //     console.log('Development mode: Simulating logout');
  //     // For development, just reload the page
  //     window.location.reload();
  //     return;
  //   }

  try {
    const xsrfToken = getCookie('XSRF-TOKEN');
    const headers: Record<string, string> = {
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    };

    if (xsrfToken) {
      headers['X-XSRF-TOKEN'] = xsrfToken;
    }

    const response = await fetch(`/logout`, {
      method: 'POST',
      credentials: 'include',
      headers,
    });

    if (!response.ok) {
      console.log('Response: ', response);
      throw new Error('Failed to logout');
    }
    // window.location.href = `http://localhost:7090`;
  } catch (error) {
    console.error('Error during logout:', error);
    throw error;
  }
};
